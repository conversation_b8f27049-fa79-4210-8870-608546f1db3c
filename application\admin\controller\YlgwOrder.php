<?php

namespace app\admin\controller;


use app\common\controller\Backend;
/**
 * 开通养老顾问记录
 *
 * @icon fa fa-user-plus
 */
class YlgwOrder extends Backend
{

    /**
     * YlgwOrder模型对象
     * @var \app\admin\model\xiluedu\CourseOrder
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\xiluedu\CourseOrder;
        
        // 定义开通方式列表
        $this->view->assign("platformList", [
            'quota' => '额度开通',
            'admin' => '管理员开通', 
            'ylgw' => '养老顾问开通', // 重新加入，因为数据中可能存在该类型
            'wxmin' => '自主购买(小程序)'
        ]);
        
        // 定义支付状态列表
        $this->view->assign("payStatusList", [
            '1' => '待支付',
            '2' => '已支付'
        ]);
        
        // 定义支付方式列表
        $this->view->assign("payTypeList", [
            '0' => '无',
            '1' => '微信',
            '2' => '余额',
            '3' => '额度支付'
        ]);
    }

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            
            $where = is_array($where) ? $where : [];
            $filter = $this->request->get("filter");
            $filter = json_decode($filter, true) ?: [];

            if (isset($filter['platform'])) {
                if (strpos($filter['platform'], ',') !== false) {
                    $platforms = explode(',', $filter['platform']);
                    $where['platform'] = ['in', $platforms];
                } else {
                    $where['platform'] = $filter['platform'];
                }
            }

            // 处理 opener_info 搜索条件
            if (isset($filter['opener_info']) && !empty($filter['opener_info'])) {
                $openerInfo = $filter['opener_info'];
                // 模糊搜索 nickname, mobile, realname
                $userIds = \app\common\model\User::where('nickname', 'like', "%{$openerInfo}%")
                                                ->whereOr('mobile', 'like', "%{$openerInfo}%")
                                                ->whereOr('realname', 'like', "%{$openerInfo}%")
                                                ->column('id');
                // 如果找到用户ID，则添加到user_id的where条件中，并包括platform为ylgw的记录
                if (!empty($userIds)) {
                    $where['parent_id'] = ['in', $userIds];
                    // 还需要确保搜索'opener_info'时，如果platform不是'ylgw'，但'opener_info'中包含搜索词，也能被检索到
                    // 考虑到opener_info可能包含'管理员开通'等固定文本，需要更复杂的逻辑
                    // 暂时只处理user_id相关的搜索，对于非用户相关的opener_info，需要前端配合或额外逻辑
                } else {
                    // 如果没有找到匹配的用户ID，可以考虑添加一个始终不匹配的条件，避免返回所有结果
                    // 或者根据业务需求，对'管理员开通'、'自主购买'等进行特殊处理
                    $where['user_id'] = 0; // 确保不返回任何结果
                }
            }
         
            // 移除 course_id = 16 的过滤，以显示所有开通记录
            // $where['course_id'] = 16;

            $list = $this->model
                    ->with(['user', 'course'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);
            //获取执行的sql
            // $sql = $this->model->getLastSql();
            // var_dump($sql); // 调试用，查看SQL语句
            // die;
            foreach ($list as $row) {
                $row->getRelation('user')->visible(['id', 'nickname', 'mobile', 'is_sqdl', 'is_qydl', 'is_ylgw']);
                $row->getRelation('course')->visible(['id', 'name']);
                
                // 添加开通方式描述
                $row->platform_text = $this->getPlatformText($row->platform);
                
                // 添加开通者信息
                $row->opener_info = $this->getOpenerInfo($row);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 获取平台文本描述
     */
    private function getPlatformText($platform)
    {
        $platformList = [
            'quota' => '额度开通',
            'admin' => '管理员开通', 
            // 'ylgw' => '养老顾问开通',
            'wxmin' => '自主购买(小程序)'
        ];
        
        return isset($platformList[$platform]) ? $platformList[$platform] : $platform;
    }

    /**
     * 获取开通者信息
     */
    private function getOpenerInfo($row)
    {
        if ($row->platform == 'admin') {
            return '管理员开通';
        }

        if (in_array($row->platform, ['wxmin'])) {
            return '自主购买';
        }

        if ($row->platform == 'ylgw') {
            return $row->opener_info; // 直接返回opener_info，它包含了HTML代码
        }

        if ($row->platform == 'quota') {
            $user = \app\common\model\User::where('id', $row->user_id)->find();
            if (!$user || !$user->parent_id) {
                return '未知开通者';
            }

            $parent = \app\common\model\User::where('id', $user->parent_id)->find();
            if (!$parent) {
                return '未知开通者';
            }
            $parent->nickname=$parent->realname?$parent->realname:$parent->nickname;
            $role = '';
            if ($parent->is_qydl == 1) {
                $role = '城市负责人';
                 return $parent->nickname . '(' . $role . ')-' . $parent->mobile;
            } elseif ($parent->is_sqdl == 1) {
                $role = '养老院长';
                 return $parent->nickname . '(' . $role . ')-' . $parent->mobile;
            }

            return $parent->nickname .'-' . $parent->mobile;
        }

        return '未知';
    }

    /**
     * 禁用添加功能
     */
    public function add()
    {
        $this->error(__('Operation not allowed'));
    }

    /**
     * 禁用编辑功能
     */
    public function edit($ids = null)
    {
        $this->error(__('Operation not allowed'));
    }

    /**
     * 禁用删除功能
     */
    public function del($ids = "")
    {
        $this->error(__('Operation not allowed'));
    }
}
