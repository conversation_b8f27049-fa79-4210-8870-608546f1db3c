<?php
namespace app\api\controller\service;

use app\admin\model\service\area\BigCustomer;
use app\admin\model\service\area\BigCustomerGroup;
use app\admin\model\service\area\UserRule;
use app\admin\model\service\order\ServiceInsurance;
use app\api\controller\xiluedu\CourseOrder;
use app\api\model\service\Goods;
use app\api\model\service\OrderLog;
use app\api\model\service\Skill;
use app\api\model\service\User;
use app\common\controller\Api;
use app\common\library\Sms;
use app\common\model\UserArea;
use fast\Random;
use PhpOffice\PhpSpreadsheet\IOFactory;
use think\Db;
use think\Exception;

/**
 * 首页接口
 */
class Agency extends Api
{
    protected $noNeedLogin = ['getHotline'];
    protected $noNeedRight = ['*'];


    public function modifyHotline()
    {
//        $district=$this->request->param('district');
        $mobile=$this->request->param('mobile');
        $user = $this->auth->getUserinfo();
        $user_id=$user['qydl_staff'] != 0 ? $user['qydl_staff'] : $this->auth->id;
        $res=UserArea::where(['user_id'=>$user_id])->update(['mobile'=>$mobile]);
        $this->success('修改成功',$res);
    }

    public function getHotline()
    {
        $district=$this->request->param('district');
        $area=UserArea::where(['district'=>$district])->find();
        $this->success('',$area['mobile'] ?? '16638053803');
    }

    /**
     * 自己购买开通养老院长 养老院长
     * @date 2024/12/3
     * @notes
     */
    public function openAgent()
    {

        $mobile = $this->request->param('mobile');
        $code = $this->request->param('code');
//        $paytype = $this->request->param('paytype');
        $addr = $this->request->param('addr');
        (!$mobile || !$code) && $this->error('参数缺失');
        (!Sms::check($mobile, $code, 'agent')) && $this->error('验证码不正确');
        $user = model('app\common\model\User')->where('mobile',$mobile)->where('is_service',0)->find();
        if(empty($user)) $this->error('用户不存在');

        $order = \app\admin\model\xiluedu\CourseOrder::where(['user_id' => $user['id'], 'course_id' => 10,'pay_status'=>2,'kt_sqdl'=>1])->find();
        if(empty($order)){
            $this->error('您还未购买养老院长课程，请先购买课程后再开通');
        }

        // 检查用户是否已经是养老院长
        $isAlreadyAgent = (isset($user['is_sqdl']) && $user['is_sqdl'] == 1);

        // 如果用户已经是养老院长但订单kt_sqdl还是1，说明需要完善地址信息
        if($isAlreadyAgent && $order->kt_sqdl != 1) {
            $this->error('您的订单已开通过养老院长，无法重复开通');
        }
        $uid = $this->auth->id;
        //获取此城市负责人代理区域
        $quyu = model('app\common\model\UserArea')->where('addr',$addr)->find();
        $quyu_uid= empty($quyu['user_id']) ? 0 : $quyu['user_id'];
        Db::startTrans();
        try{
            if(1){//余额支付
                // 如果用户还不是养老院长，则升级为养老院长
                if(!$isAlreadyAgent) {
                    $user->is_sqdl=1;
                }

                // 更新地址信息
                $user->addr=$addr;
                $address = explode('/',$addr);
                $user->province = $address[0];
                $user->city  = $address[1];
                $user->district = $address[2]??'';

                // 更新地址信息
                $user->addr=$addr;
                $address = explode('/',$addr);
                $user->province = $address[0];
                $user->city  = $address[1];
                $user->district = $address[2]??'';
                //查询当前用户的上级是否为城市运营商
                  
                $user_parent = model('app\common\model\User')->where('id',$user['parent_id'])->where('is_qydl',1)->find();
                // --- NEW LOGIC FOR PARENT REASSIGNMENT (Always re-evaluate parent based on address) ---
               if(empty($user_parent)){

                    $city_manager_in_new_area = model('app\common\model\UserArea')->where('addr',$addr)->find(); // Find QYDL in new area
                    if($city_manager_in_new_area && $city_manager_in_new_area['user_id']) {
                        $user->parent_id = $city_manager_in_new_area['user_id'];
                        \think\Log::info("openAgent: 用户ID {$user->id} 地址更新为 {$addr}，分配给城市运营商ID {$user->parent_id}");
                    } else {
                        $user->parent_id = 0; // 没有找到城市运营商，上级设为0
                        \think\Log::info("openAgent: 用户ID {$user->id} 地址更新为 {$addr}，未找到城市运营商，上级设为0");
                    }

               }
               
               
                // --- END NEW LOGIC ---

                $user->save(); // Save the user with updated address and parent_id
            }
//
//            else{
//                $re = \addons\service\library\Pay::payOrder(['amount'=>$price,'orderid'=>$orderId,'title'=>'开通养老院长'],$paytype,$uid,0);
//            }
            $order->kt_sqdl=0;
            $order->save();
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error('操作失败',$e->getMessage());
        }

        $message = $isAlreadyAgent ? '信息更新成功' : '开通成功';
        $this->success($message,$user);
    }


    public function addBigGroup()
    {
        $name=$this->request->param('name');
        $remark=$this->request->param('remark');
        if(empty($name)) $this->error('请输入名称');
        $r=BigCustomerGroup::create(['user_id'=>$this->auth->id,'name'=>$name,'remark'=>$remark]);
        $this->success('ok',$r);
    }


    public function bigGroupList()
    {
       $r=BigCustomerGroup::where('user_id',$this->auth->id)->select();
       $this->success('ok',$r);
    }

    public function bigCustomerImport()
    {
        $file=$this->request->file('file');
        $group_id=$this->request->param('group_id');
        if(!BigCustomerGroup::get($group_id)) $this->error('参数错误');
        if(empty($file)) $this->error('请选择导入文件');
        $info = $file->move(ROOT_PATH . 'public' . DS . 'upload');
        $spreadsheet = IOFactory::load(ROOT_PATH . 'public' . DS . 'upload/'.$info->getSaveName());
        $sheetData = $spreadsheet->getSheet(0)->toArray();
        $i=0;
        foreach ($sheetData as $k=>$row) {
            if($k<=1) continue;
            if(empty($row[1])) continue;
            $year=substr($row[3], 0, 4);
            $age=date('Y') - (int)$year;
            $data=[
                'user_id'=>$this->auth->id,
                'group_id'=>$group_id,
                'name'=>$row[1],
                'gender'=>$row[2],
                'birthday'=>$row[3],
                'phone'=>$row[4],
                'province'=>$row[5],
                'city'=>$row[6],
                'district'=>$row[7],
                'addr'=>$row[8],
                'disease'=>$row[9],
                'age'=>$age
            ];
            BigCustomer::create($data);
            $i++;
        }
        $this->success('ok',$i);
    }


    public function  bigCustomerList()
    {
        $age_range=$this->request->param('age_range');
        $city=$this->request->param('city');
        $district=$this->request->param('district');
        $gender=$this->request->param('gender');
        $group_id= $this->request->param('group_id');
        $name = $this->request->param('name');
        $where=[];
        if($age_range) $where['age']=['between',$age_range];
        if($city) $where['city']=$city;
        if($district) $where['district']=$district;
        if($gender) $where['gender']=$gender;
        if($group_id) $where['group_id']=$group_id;
        if($name) $where['name']=['like',"%$name%"];
        $list=BigCustomer::where($where)->paginate(10);
        $this->success('ok',$list);

    }

    public function  bigCustomerDel()
    {
        $id=$this->request->param('id');
        $bc=BigCustomer::get($id);
        if(empty($bc)) $this->error('参数错误');
        $this->success('ok', $bc->delete());
    }

    public function areaInsList()
    {
        $district=$this->request->param('district');
        $user =$this->auth->getUserinfo();
        $user_id2 =$user['qydl_staff']!=0 ? $user['qydl_staff'] :$this->auth->id;
        $district = model('app\common\model\UserArea')->where('user_id',$user_id2)->value('district');

        $year=$this->request->param('year');
        $month=$this->request->param('month');

        $start_date=date("Y-m-d", strtotime("{$year}-{$month}-01"));
        $end_date=date("Y-m-d", strtotime("{$year}-{$month}-" . date("t", mktime(0, 0, 0, $month, 1, $year))));

        $user_ids=\app\api\model\service\Skill::where(['district'=>$district])->column('user_id');
        $list=ServiceInsurance::where(['user_id'=>['in',$user_ids],'createtime'=>['between',[strtotime($start_date),strtotime($end_date. ' 23:59:59')]]])->select();

        foreach ($list as $k=>$v){
            $list[$k]['skill_info']=\app\api\model\service\Skill::where(['user_id'=>$v['user_id']])->field('name,user_image')->find();
        }
        $result['total_people']=ServiceInsurance::where(['user_id'=>['in',$user_ids],'createtime'=>['between',[strtotime($start_date),strtotime($end_date. ' 23:59:59')]]])->group('user_id')->count();
        $result['total_money']=ServiceInsurance::where(['user_id'=>['in',$user_ids],'createtime'=>['between',[strtotime($start_date),strtotime($end_date. ' 23:59:59')]]])->sum('money');
        $result['list']=$list;
        $this->success('ok',$result);
    }


    /**
     * 城市负责人开通员工
     * @notes
     */
    public function openStaff()
    {
        $mobile = $this->request->param('mobile');
        $code = $this->request->param('code');
        $name = $this->request->param('name');
        $job = $this->request->param('job');
        $rule_ids = $this->request->param('rule_ids');
        (!$mobile || !$code || !$rule_ids || !$name) &&
        $this->error('参数缺失');
        (!Sms::check($mobile, $code, 'staff')) && $this->error('验证码不正确');
        $user = model('app\common\model\User')->where('mobile',$mobile)->where('is_service',0)->find();
        if(empty($user)) $this->error('用户不存在');
        if($user->is_qydl==1 || $user->is_sqdl ==1) $this->error('添加失败，该用户有代理身份');
        if(isset($user['qydl_id']) && $user['qydl_id'] != 0){
            $this->error('此用户已是员工，请勿重复开通');
        }
        $ag_uid = $this->auth->id;
        //获取此城市负责人代理区域
        $userInfo=$this->auth->getUserInfo();
        if($userInfo['is_qydl']!=1)  $this->error('您还不是城市负责人');
        $user_rule=UserRule::where(['user_id'=>$user->id])->find();
        if(empty($user_rule)){
            $r=UserRule::create([
                'user_id'=>$user->id,
                'qydl_id'=>$ag_uid,
                'name'=>$name,
                'job'=>$job,
                'rule_ids'=>$rule_ids
            ]);
            $user->qydl_staff=$ag_uid;
            $user->save();
        }else{
            $user_rule->rule_ids=$rule_ids;
            $user_rule->name=$name;
            $user_rule->job=$job;
            $r=$user_rule->save();
        }
        $this->success('ok',$r);
    }


    /**
     * 城市负责人编辑员工
     * @notes
     */
    public function editStaff()
    {
        $mobile = $this->request->param('mobile');
        $code = $this->request->param('code');
        $name = $this->request->param('name');
        $job = $this->request->param('job');
        $rule_ids = $this->request->param('job');
        (!$mobile || !$code || !$rule_ids || !$name) && $this->error('参数缺失');
        (!Sms::check($mobile, $code, 'agent')) && $this->error('验证码不正确');
        $user = model('app\common\model\User')->where('mobile',$mobile)->find();
        if(empty($user)) $this->error('用户不存在');
        if(isset($user['qydl_id']) && $user['qydl_id'] != 0){
            $this->error('此用户已是员工，请勿重复开通');
        }
        $ag_uid = $this->auth->id;
        //获取此城市负责人代理区域
        $userInfo=$this->auth->getUserInfo();
        if($userInfo['is_qydl']!=1)  $this->error('您还不是城市负责人');
        $user_rule=UserRule::where(['user_id'=>$user->id])->find();
        if(empty($user_rule)){
            UserRule::create([
                'user_id'=>$user->id,
                'qydl_id'=>$ag_uid,
                'name'=>$name,
                'job'=>$job,
                'rule_ids'=>$rule_ids
            ]);
        }else{
            $user_rule->rule_ids=$rule_ids;
            $user_rule->name=$name;
            $user_rule->job=$job;
            $user_rule->save();
        }
        $this->success('ok');
    }

    public function staffList()
    {
        $name=$this->request->param('name');
        $where['qydl_id']=$this->auth->id;
        if(!empty($name)) $where['name']=['like','%'.$name.'%'];
        $list=UserRule::where($where)->select();
        foreach ($list as &$v){
            $v['user_info']=\app\common\model\User::getSimpleUser($v['user_id']);
        }
        $this->success('ok',$list);
    }

    public function staffDetail()
    {
        $id=$this->request->param('user_id');
        $where['user_id']=$id;
        $list=UserRule::where($where)->find();
        if(empty($list)) $this->success('ok',null);
        $list['user_info']=\app\common\model\User::getSimpleUser($list['user_id']);
        $this->success('ok',$list);
    }
    public function delStaff()
    {
        $id=$this->request->param('id');
        $res=UserRule::where(['id'=>$id])->find();
        if(empty($res)) $this->error('删除失败');
        \app\common\model\User::where(['id'=>$res['user_id']])->update(['qydl_staff'=>0]);
        $res->delete();
        $this->success('ok',$res);

    }

    public function ruleList()
    {
        $list=Db::table("fa_service_rule")->where('switch',1)->order('id asc')->select();
        $this->success('ok',$list);
    }

    public function agentInfo()
    {
        $user_id=$this->request->param('user_id');
        $area=UserArea::where(['user_id'=>$user_id ?? $this->auth->id])->find();
        $this->success('ok',$area);
    }

    public function dsOrderConfirm()
    {
        $order_id=$this->request->param('order_id');
        $order=\app\api\model\service\Order::get($order_id);
        if(empty($order) || $order->goods_type!=2) $this->error('订单信息错误');
        if($order->total_cost_sy<1) $this->error('订单次数不足');
        if($order->user_confirm_time){
            $now=strtotime(date("Y-m-d"));
            if($order->user_confirm_time> $now) $this->error('确认失败');
        }
        $order->total_cost_sy-=1;
        $order->total_cost_use_num+=1;
        if($order->total_cost_sy==0) $order->user_confirm=1;
        $order->save();

        $order['sumprice'] = truncateDecimal($order['sumprice']/$order['total_cost_seconds']);//项目价格
//        $data['goods_total_price'] = truncateDecimal($order['goods_total_price']/$order['total_cost_seconds']);//项目价格
        $order['coupon_price'] = truncateDecimal($order['coupon_price']/$order['total_cost_seconds']);//优惠券金额
//        OrderLog::create(['order_id'=>$order_id,'user_id'=>$order['user_id'],'type'=>20,'content'=>'用户确认订单']);
        //发放分佣start
        $goods = Goods::where('id',$order['goods_id'])->field('id,shequ_bili,quyu_bili,service_bili')->find();
        if($goods['service_bili'] && $goods['quyu_bili']>0){
            $service_money = truncateDecimal($order['sumprice'] * ($goods['service_bili'] / 100));
        }else{
            $service_money = truncateDecimal($order['sumprice'] * (config('site.fwz_bili') / 100));
        }
        $residue=$order['sumprice']-$service_money-$order['coupon_price'];

        $pt_rate=100;
        if($goods['shequ_bili'] && $goods['shequ_bili']>0){
            $sq_money = truncateDecimal($residue * ($goods['shequ_bili'] / 100));
        }else{
            $sq_money = truncateDecimal($residue * (config('site.shequ_bili') / 100));
        }
        if($goods['quyu_bili'] && $goods['quyu_bili']>0){
            $qy_money = truncateDecimal($residue * ($goods['quyu_bili'] / 100));
        }else{
            $qy_money = truncateDecimal($residue * (config('site.quyu_bili') / 100));
        }
        //发放养老院长佣金
        if($sq_money > 0){
            $user = User::where('id',$order['user_id'])->field('id,parent_id')->find();
            if($user['parent_id']){
                $sq_user = \app\common\model\User::getOneSq($user['parent_id']);
                if(!empty($sq_user)) {
                    \app\common\model\User::money($sq_money, $sq_user['id'], '养老院长佣金', 2, $order['orderId'], 'fenyong');
                    $residue -= $sq_money;
                }
            }
        }
        //发放城市负责人佣金
        $qy_user_id = \app\common\model\UserArea::where('district',$order['district'])->value('user_id');
        if($qy_user_id && $qy_money > 0){
            \app\common\model\User::money($qy_money,$qy_user_id,'城市负责人佣金',2,$order['orderId'],'fenyong');
            $residue-=$qy_money;
        }
        //放发平台收益
        if($residue>0){
            \app\common\model\User::money($residue,1,'平台收益',2,$order['orderId'],'fenyong');
        }
        //给服务者发放佣金
        if($service_money > 0){
            $sk=Skill::get($order['skill_id']);
            \app\common\model\User::money($service_money,$sk['user_id'],'服务者获得佣金',2,$order['orderId'],'fenyong');
        }

        \app\api\model\service\Order::where(['id'=>$order_id])->update([
            'user_confirm_time'=>time(),
            'shequ_money'=>$order->shequ_money+$sq_money,
            'quyu_money'=>$order->quyu_money+$qy_money,
            'service_money'=> $order->service_money+$service_money
        ]);
        $this->success('ok',$order);

    }

    public function bigCustomerOrder()
    {
        $ids=$this->request->param('ids/a');
        $customers= BigCustomer::where('id','in',$ids)->select();
        foreach ($customers as $v){
            $v->status=1;
            $v->save();
        }

    }

}
