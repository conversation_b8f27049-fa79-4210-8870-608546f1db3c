<?php

namespace app\admin\controller\user;

use app\common\controller\Backend;
use app\common\library\Auth;
use think\Session;
use app\common\model\xiluedu\CourseOrder;
use think\Log;

/**
 * 会员管理
 *
 * @icon fa fa-user
 */
class User extends Backend
{

    protected $relationSearch = true;
    protected $searchFields = 'id,username,nickname';
    /**
     * Selectpage可显示的字段
     */
    protected $selectpageFields = ['id','username','nickname','mobile','email','avatar'];

    /**
     * @var \app\admin\model\User
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\User;
        $this->view->assign("sqdlList", $this->model->getSqdlList());
        $this->view->assign("qydlList", $this->model->getQydlList());
        $this->view->assign("shopTypeList", $this->model->getShopTypeList());
        $this->view->assign("ylgwList", $this->model->getYlgwList());
        $this->view->assign("typeList", [1=>'养老院长',0=>'普通用户']);

        // 检查当前登录管理员是否有权限管理养老顾问开通额度
        // 在后台管理系统中，通常管理员都有权限管理用户信息
        // 这里设置为 true，表示管理员可以编辑养老顾问开通额度
        $canManageQuota = true;
        $this->view->assign("canManageQuota", $canManageQuota);
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
             
                return $this->selectpage();
            }

            $filter = json_decode($this->request->get("filter", ''), true);
            $filter['is_service'] =0;
            if(!empty($filter['level'])){
                if($filter['level'] == 1){
                    $filter['is_sqdl']=1;
                }elseif ($filter['level'] == 2){
                    $filter['is_qydl']=1;
                }elseif ($filter['level'] == 4){
                    $filter['is_ylgw']=1;
                }elseif ($filter['level'] == 3){
                    $filter['is_sqdl']=0;
                    $filter['is_qydl']=0;
                    $filter['is_ylgw']=0;
                    $filter['is_shop']=0;
                }
            }
            unset($filter['level']);
            $this->request->get(['filter' => json_encode($filter, true)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->with('group')
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
            foreach ($list as $k => &$v) {
                $v->avatar = 'https://ylzjstatics.jiaqingfu.com.cn'.$v->avatar;
                $v->hidden(['password', 'salt']);
                $sf=$v['is_sqdl']==1?'[养老院长]':'';
                $sf.=$v['is_qydl']==1?'[城市负责人]':'';
                $sf.=$v['is_ylgw']==1?'[养老顾问]':'';
                $sf.=$v['is_shop']==1?'[供货商]':'';
                // 如果用户被设置为养老顾问，则在shenfen字段中添加标记
                if ($v['is_ylgw'] == 1 && strpos($sf, '[养老顾问]') === false) {
                    $sf .= '[养老顾问]';
                }
                $sf = empty($sf)?'普通用户':$sf;
                $v['shenfen']=$sf;
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    public function ylgw($ids=null)
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            $row = $this->model->get(Session::get('user_id'));
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            $filter = $this->request->get("filter", '');
            $filter= json_decode($filter, true);

            // 处理养老顾问tab的过滤
            if (isset($filter['is_sqdl']) && $filter['is_sqdl'] == '2') {
                $filter['is_ylgw'] = 1;
                unset($filter['is_sqdl']); // 移除is_sqdl条件，避免冲突
            } else {
                $filter['is_sqdl']=$filter['is_sqdl']??1;
            }

            $where2=$userIds=[];

            if(isset($row['id'])) $where2['parent_id']=$row['id'];

            //城市负责人用户
            if($row['is_qydl']==1 && $filter['is_sqdl']==0){

                $pids=$this->model->where(['is_sqdl'=>1])->where($where2)->column('id');
                foreach ($pids as $pid) {
                    $userIds +=  \app\common\model\User::getAllNonAgentDescendants($pid);
                }
                list($where, $sort, $order, $offset, $limit) = $this->buildparams();
                $list = $this->model
                    ->with('group')
                    ->where($where)
                    ->where('is_sqdl',$filter['is_sqdl'] ?? 1)
//                    ->whereIn('parent_id',$pids)
                    ->whereIn('user.id',$userIds)
//                ->where('user.is_sqdl',0)
//                ->where('user.is_qydl',0)
//                ->where('user.is_shop',0)
                    ->order($sort, $order)
                    ->paginate($limit);
                foreach ($list as $k => $v) {
                    $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                    $v->hidden(['password', 'salt']);
                }
                $result = array("total" => $list->total(), "rows" => $list->items());

                return json($result);
            }


            if($row['is_sqdl']==1 && $filter['is_sqdl']==0){

                $userIds +=  \app\common\model\User::getAllNonAgentDescendants($row['id']);
                list($where, $sort, $order, $offset, $limit) = $this->buildparams();
                $list = $this->model
                    ->with('group')
                    ->where($where)
                    ->where('is_sqdl',$filter['is_sqdl'] ?? 1)
                    ->whereIn('user.id',$userIds)
                    ->order($sort, $order)
                    ->paginate($limit);
                foreach ($list as $k => $v) {
                    $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                    $v->hidden(['password', 'salt']);
                }
                $result = array("total" => $list->total(), "rows" => $list->items());

                return json($result);
            }
         
            // 处理养老顾问tab的数据查询
            if (isset($filter['is_ylgw']) && $filter['is_ylgw'] == 1) {
                list($where, $sort, $order, $offset, $limit) = $this->buildparams();
                $list = $this->model
                    ->with('group')
                    ->where($where)
                    ->where('is_ylgw', 1)
                    ->order($sort, $order)
                    ->paginate($limit);
                 echo   $this->model->getLastSql();die;
                foreach ($list as $k => $v) {
                    $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                    $v->hidden(['password', 'salt']);
                }
                $result = array("total" => $list->total(), "rows" => $list->items());

                return json($result);
            }


            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->with('group')
                ->where($where)
                ->where($where2)
                ->where('is_sqdl',$filter['is_sqdl'] ?? 1)
//                ->where('user.is_sqdl',0)
//                ->where('user.is_qydl',0)
//                ->where('user.is_shop',0)
                ->order($sort, $order)
                ->paginate($limit);
            foreach ($list as $k => $v) {
                $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                $v->hidden(['password', 'salt']);
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        Session::set('user_id',$ids);
        return $this->view->fetch();
    }

    public function hhr()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->with('group')
                ->where($where)
                ->where('user.is_qydl',1)
                ->where('user.is_shop',0)
                ->order($sort, $order)
                ->paginate($limit);
            foreach ($list as $k => $v) {
                $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                $v->hidden(['password', 'salt']);
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    public function hhr2()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->with('group')
                ->where($where)
                ->where('user.is_qydl',1)
                ->where('user.is_shop',0)
                ->order($sort, $order)
                ->paginate($limit);
            foreach ($list as $k => $v) {
                $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                $v->hidden(['password', 'salt']);
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    public function shop()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->with('group')
                ->where($where)
                ->where('user.is_shop',1)
                ->order($sort, $order)
                ->paginate($limit);
            foreach ($list as $k => $v) {
                $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                $v->hidden(['password', 'salt']);
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $this->token();

            // 获取提交的数据
            $params = $this->request->post('row/a');
            if (empty($params)) {
                $this->error(__('Parameter %s can not be empty', ''));
            }

            // 身份唯一性验证
            $identityCount = 0;
            $identityFields = ['is_sqdl', 'is_qydl', 'is_ylgw', 'is_shop'];

            foreach ($identityFields as $field) {
                if (isset($params[$field]) && $params[$field] == 1) {
                    $identityCount++;
                }
            }

            if ($identityCount > 1) {
                $this->error('用户只能有一个身份，请选择其中一个身份');
            }

            // 如果验证通过，继续执行父类的add方法
        }
        return parent::add();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        // 获取提交的数据
        $params = $this->request->post('row/a');
        // 确保params是一个数组，即使在GET请求时
        if (!is_array($params)) {
            $params = [];
        }

        if ($this->request->isPost()) {
            $this->token();

            if (empty($params)) {
                $this->error(__('Parameter %s can not be empty', ''));
            }

            // 身份唯一性验证
            $identityCount = 0;
            $identityFields = ['is_sqdl', 'is_qydl', 'is_ylgw', 'is_shop'];

            foreach ($identityFields as $field) {
                if (isset($params[$field]) && $params[$field] == 1) {
                    $identityCount++;
                }
            }

            if ($identityCount > 1) {
                $this->error('用户只能有一个身份，请选择其中一个身份');
            }

            // 如果验证通过，继续执行父类的edit方法
        }

        $row = $this->model->get($ids);
       
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $original_is_ylgw = $row->is_ylgw; // 获取原始is_ylgw状态

        $atid = $row['is_sqdl'] == 1 ? '25' : '25';
        $atid = $row['is_qydl'] == 1 ? '24' : $atid;
        $this->view->assign('xy_url', '/index/index/sys_text?id='.$atid);
        $this->view->assign('groupList', build_select('row[group_id]', \app\admin\model\UserGroup::column('id,name'), $row['group_id'], ['class' => 'form-control selectpicker']));
          $current_is_ylgw = isset($params['is_ylgw']) ? (int)$params['is_ylgw'] : 0;
          
        if ($current_is_ylgw == 1 && $original_is_ylgw == 0) {
            // 检查是否已经存在该用户的养老顾问开通记录
        
                // 确保 nickname 和 mobile 存在
                $nickname = isset($params['nickname']) ? $params['nickname'] : '未知昵称';
                $mobile = isset($params['mobile']) ? $params['mobile'] : '未知手机号';

                try{
  // 创建新的养老顾问开通记录
                $newOrder = CourseOrder::create([
                    'platform'      => 'admin',
                    'user_id'       => $ids,
                    'order_no'      => "YLGW" . date("YmdHis") . mt_rand(10, 9999), // 自定义订单号
                    'course_id'     => 16, // 默认0，表示非课程购买
                    'total_price'   => 0,
                    'pay_price'     => 0,
                    'user_coupon_id'=> 0,
                    'favourable_price'=> 0,
                    'pay_status'    => 2, // 直接设置为已支付/已开通
                    'createtime'    => time(),
                    'updatetime'    => time(),
                    'is_service'    => 0
                ]);
                }catch (\Exception $e) {
                    Log::error('Error creating CourseOrder: ' . $e->getMessage());
                    $this->error('创建养老顾问开通记录失败，请稍后重试。');
                }
              

                if ($newOrder) {
                    Log::info('CourseOrder created successfully with ID: ' . $newOrder->id);
                } else {
                    Log::error('Failed to create CourseOrder.');
                }
            }
        $result = parent::edit($ids); // 执行父类的编辑操作

        // 如果用户被设置为养老顾问，并且之前不是养老顾问，则记录开通
      
        
        return $result;
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        Auth::instance()->delete($row['id']);
        $this->success();
    }

}
