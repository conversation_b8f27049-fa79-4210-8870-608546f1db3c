<div class="panel panel-default panel-intro">
    {:build_heading()}
<!--    <div class="panel-heading">-->
<!--        {:build_heading(null,FALSE)}-->
<!--        <ul class="nav nav-tabs" data-field="is_shop">-->
<!--            <li class="{:$Think.get.is_shop === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>-->
<!--            {foreach name="shopTypeList" item="vo"}-->
<!--            <li class="{:$Think.get.is_shop === (string)$key ? 'active' : ''}"><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>-->
<!--            {/foreach}-->
<!--        </ul>-->
<!--    </div>-->

    　　<div class="panel-heading">
    {:build_heading(null,FALSE)}
    <ul class="nav nav-tabs"  data-field="status">
        <li class="active"><a href="#1" data-value="1" data-toggle="tab">养老院长</a></li>
        <li class=""><a href="#0" data-value="0" data-toggle="tab">普通用户</a></li>
        <li class=""><a href="#2" data-value="2" data-toggle="tab">养老顾问</a></li>
<!--        {foreach name="typeList" item="vo"}-->
<!--        <li><a href="#{$key}" data-toggle="tab">{$vo}</a></li>-->
<!--        {/foreach}-->
    </ul>
    　　</div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh,edit,del')}
                        <div class="dropdown btn-group {:$auth->check('user/user/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>
                            </ul>
                        </div>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('user/user/edit')}"
                           data-operate-del="{:$auth->check('user/user/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
